using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Channels;
using HolyBless.Services;
using HolyBless.Interfaces;
using HolyBless.Enums;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Albums.Events
{
    public class AlbumListRequestedEventHandler : ILocalEventHandler<AlbumListRequestedEvent>
    {
        private readonly IRepository<Album, int> _albumRepository;
        private readonly IRepository<Channel, int> _channelRepository;
        private readonly IRequestContextService _requestContextService;
        private readonly IObjectMapper _objectMapper;
        private readonly ICachedFileUrlAppService _cachedFileUrlAppService;

        public AlbumListRequestedEventHandler(
            IRepository<Album, int> albumRepository,
            IRepository<Channel, int> channelRepository,
            IRequestContextService requestContextService,
            IObjectMapper objectMapper,
            ICachedFileUrlAppService cachedFileUrlAppService)
        {
            _albumRepository = albumRepository;
            _channelRepository = channelRepository;
            _requestContextService = requestContextService;
            _objectMapper = objectMapper;
            _cachedFileUrlAppService = cachedFileUrlAppService;
        }

        public async Task HandleEventAsync(AlbumListRequestedEvent eventData)
        {
            var input = eventData.Input;
            var queryable = await _albumRepository.GetQueryableAsync();
            var channelId = input.ChannelId;
            
            if (channelId == null && !string.IsNullOrEmpty(input.ChannelContentCode))
            {
                var lang = _requestContextService!.GetLanguageCode();
                var channel = await _channelRepository.FirstOrDefaultAsync(x => x.LanguageCode == lang && x.ContentCode == input.ChannelContentCode);
                if (channel != null)
                {
                    channelId = channel.Id;
                }
            }

            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == channelId)
                .WhereIf(input.AlbumType.HasValue, x => x.AlbumType == input.AlbumType);

            var totalCount = await query.CountAsync();

            var albums = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var albumDtos = _objectMapper.Map<List<Album>, List<AlbumDto>>(albums);
            await FillThumbnailUrls(albumDtos);

            eventData.Result = new PagedResultDto<AlbumDto>(totalCount, albumDtos);
        }

        private async Task FillThumbnailUrls<T>(List<T> albumDtos) where T : IHaveThumbnail
        {
            var preferProvider = ProviderCodeConstants.CloudFlare;
            if (_cachedFileUrlAppService == null || albumDtos.Count == 0)
            {
                return;
            }

            var urls = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
                albumDtos.Select(x => x.ThumbnailFileId).ToList(),
                preferProvider
            );
            foreach (var al in albumDtos)
            {
                if (al.ThumbnailFileId == null)
                {
                    continue;
                }
                al.ThumbnailUrl = urls.TryGetValue(al.ThumbnailFileId.Value, out var url) ? url : null;
            }
        }
    }
}
