using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Channels;
using HolyBless.EventHandlerBase;
using HolyBless.Services;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Albums.Events
{
    public class AlbumListRequestedEventHandler : BaseContextEventHandler, ILocalEventHandler<AlbumListRequestedEvent>
    {
        private readonly IRepository<Album, int> _albumRepository;
        private readonly IRepository<Channel, int> _channelRepository;

        public AlbumListRequestedEventHandler(
            IRepository<Album, int> albumRepository,
            IRepository<Channel, int> channelRepository,
            IRequestContextService requestContextService,
            IObjectMapper objectMapper,
            ICachedFileUrlAppService cachedFileUrlAppService)
            : base(objectMapper, cachedFileUrlAppService, requestContextService)
        {
            _albumRepository = albumRepository;
            _channelRepository = channelRepository;
        }

        public async Task HandleEventAsync(AlbumListRequestedEvent eventData)
        {
            var input = eventData.Input;
            var queryable = await _albumRepository.GetQueryableAsync();
            var channelId = input.ChannelId;
            
            if (channelId == null && !string.IsNullOrEmpty(input.ChannelContentCode))
            {
                var lang = _requestContextService!.GetLanguageCode();
                var channel = await _channelRepository.FirstOrDefaultAsync(x => x.LanguageCode == lang && x.ContentCode == input.ChannelContentCode);
                if (channel != null)
                {
                    channelId = channel.Id;
                }
            }

            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == channelId)
                .WhereIf(input.AlbumType.HasValue, x => x.AlbumType == input.AlbumType);

            var totalCount = await query.CountAsync();

            var albums = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var albumDtos = _objectMapper.Map<List<Album>, List<AlbumDto>>(albums);
            await FillThumbnailUrls(albumDtos);

            eventData.Result = new PagedResultDto<AlbumDto>(totalCount, albumDtos);
        }
    }
}
