using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Channels;
using HolyBless.EventHandlerBase;
using HolyBless.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Albums.Events
{
    public class AlbumListRequestedEventHandler : BaseContextEventHandler, ILocalEventHandler<AlbumListRequestedEvent>
    {
        private readonly IReadOnlyRepository<Album, int> _albumRepository;
        private readonly IReadOnlyRepository<Channel, int> _channelRepository;
        private readonly ILogger<AlbumListRequestedEventHandler> _logger;

        public AlbumListRequestedEventHandler(
            IReadOnlyRepository<Album, int> albumRepository,
            IReadOnlyRepository<Channel, int> channelRepository,
            IRequestContextService requestContextService,
            IObjectMapper objectMapper,
            ICachedFileUrlAppService cachedFileUrlAppService,
            ILogger<AlbumListRequestedEventHandler> logger)
            : base(objectMapper, cachedFileUrlAppService, requestContextService)
        {
            _albumRepository = albumRepository;
            _channelRepository = channelRepository;
            _logger = logger;
        }

        public async Task HandleEventAsync(AlbumListRequestedEvent eventData)
        {
            _logger.LogInformation("AlbumListRequestedEventHandler.HandleEventAsync called");

            var input = eventData.Input;
            var queryable = await _albumRepository.GetQueryableAsync();
            var channelId = input.ChannelId;
            
            if (channelId == null && !string.IsNullOrEmpty(input.ChannelContentCode))
            {
                var lang = _requestContextService!.GetLanguageCode();
                var channel = await _channelRepository.FirstOrDefaultAsync(x => x.LanguageCode == lang && x.ContentCode == input.ChannelContentCode);
                if (channel != null)
                {
                    channelId = channel.Id;
                }
            }

            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == channelId)
                .WhereIf(input.AlbumType.HasValue, x => x.AlbumType == input.AlbumType);

            var totalCount = await query.CountAsync();

            var albums = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var albumDtos = _objectMapper.Map<List<Album>, List<AlbumDto>>(albums);
            await FillThumbnailUrls(albumDtos);

            eventData.Result = new PagedResultDto<AlbumDto>(totalCount, albumDtos);
        }
    }
}
