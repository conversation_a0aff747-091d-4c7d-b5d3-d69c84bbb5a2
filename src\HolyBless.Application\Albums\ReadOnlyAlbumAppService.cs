using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Albums.Events;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Local;

namespace HolyBless.Albums
{
    [AllowAnonymous]
    public class ReadOnlyAlbumAppService(
        IRepository<Album, int> albumRepository,
        IRepository<AlbumToFile> albumToFileRepository,
        IRepository<BucketFile, int> bucketFileRepository,
        IRepository<Channel, int> channelRepository,
        IRequestContextService requestContextService,
        ICachedFileUrlAppService cachedFileUrlAppService,
        ILocalEventBus localEventBus
            ) : HolyBlessAppService(cachedFileUrlAppService, requestContextService), IReadOnlyAlbumAppService
    {
        protected readonly IRepository<Album, int> _albumRepository = albumRepository;
        protected readonly IRepository<AlbumToFile> _albumToFileRepository = albumToFileRepository;
        protected readonly IRepository<BucketFile, int> _bucketFileRepository = bucketFileRepository;
        protected readonly IRepository<Channel, int> _channelRepository = channelRepository;
        protected readonly ILocalEventBus _localEventBus = localEventBus;



        /// <summary>
        /// [Public] Get a paginated list of albums based on search criteria.
        /// When a channel source is Album, UI should call this method twice to render Album listing page
        /// One call with AlbumType:Audio and another with AlbumType:Video.
        /// if ChannelId is null, it will try to find the channel by ChannelContentCode and LanguageCoe (from the request header).
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<AlbumDto>> GetListAsync(AlbumSearchDto input)
        {
            var albumListEvent = new AlbumListRequestedEvent(input);
            await _localEventBus.PublishAsync(albumListEvent);
            return albumListEvent.Result ?? new PagedResultDto<AlbumDto>(0, new List<AlbumDto>());
        }

        /// <summary>
        /// [Public] When an album is selected, get all files' information within the album
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task<AlbumAggregateDto> GetAlbumFilesAsync(int albumId)
        {
            var albumFilesEvent = new AlbumFilesRequestedEvent(albumId);
            await _localEventBus.PublishAsync(albumFilesEvent);
            return albumFilesEvent.Result ?? new AlbumAggregateDto();
        }

        
    }
}