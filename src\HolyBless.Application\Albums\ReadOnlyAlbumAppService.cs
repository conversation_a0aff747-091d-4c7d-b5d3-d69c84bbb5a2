using HolyBless.Albums.Dtos;
using HolyBless.Albums.Events;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Local;

namespace HolyBless.Albums
{
    [AllowAnonymous]
    public class ReadOnlyAlbumAppService(
        ILocalEventBus localEventBus,
        ILogger<ReadOnlyAlbumAppService> logger
            ) : HolyBlessAppService, IReadOnlyAlbumAppService
    {
        protected readonly ILocalEventBus _localEventBus = localEventBus;
        protected readonly ILogger<ReadOnlyAlbumAppService> _logger = logger;

        /// <summary>
        /// [Public] Get a paginated list of albums based on search criteria.
        /// When a channel source is Album, UI should call this method twice to render Album listing page
        /// One call with AlbumType:Audio and another with AlbumType:Video.
        /// if ChannelId is null, it will try to find the channel by ChannelContentCode and LanguageCoe (from the request header).
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<AlbumDto>> GetListAsync(AlbumSearchDto input)
        {
            _logger.LogInformation("GetListAsync called with ChannelId: {ChannelId}, AlbumType: {AlbumType}",
                input.ChannelId, input.AlbumType);

            var albumListEvent = new AlbumListRequestedEvent(input);

            _logger.LogInformation("Publishing AlbumListRequestedEvent");
            await _localEventBus.PublishAsync(albumListEvent);

            _logger.LogInformation("Event published. Result is {IsNull}. TotalCount: {TotalCount}, ItemCount: {ItemCount}",
                albumListEvent.Result == null ? "NULL" : "NOT NULL",
                albumListEvent.Result?.TotalCount ?? 0,
                albumListEvent.Result?.Items?.Count ?? 0);

            return albumListEvent.Result ?? new PagedResultDto<AlbumDto>(0, new List<AlbumDto>());
        }

        /// <summary>
        /// [Public] When an album is selected, get all files' information within the album
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task<AlbumAggregateDto> GetAlbumFilesAsync(int albumId)
        {
            var albumFilesEvent = new AlbumFilesRequestedEvent(albumId);
            await _localEventBus.PublishAsync(albumFilesEvent);
            return albumFilesEvent.Result ?? new AlbumAggregateDto();
        }

        /// <summary>
        /// Test method to debug event handler issues
        /// </summary>
        public async Task<string> TestEventHandlerAsync()
        {
            _logger.LogInformation("TestEventHandlerAsync called");

            var input = new AlbumSearchDto
            {
                MaxResultCount = 10,
                SkipCount = 0
            };

            var albumListEvent = new AlbumListRequestedEvent(input);

            _logger.LogInformation("About to publish test event");
            await _localEventBus.PublishAsync(albumListEvent);
            _logger.LogInformation("Test event published");

            var hasResult = albumListEvent.Result != null;
            var totalCount = albumListEvent.Result?.TotalCount ?? 0;
            var itemCount = albumListEvent.Result?.Items?.Count ?? 0;

            var result = $"Event Handler Test - HasResult: {hasResult}, TotalCount: {totalCount}, ItemCount: {itemCount}";
            _logger.LogInformation(result);

            return result;
        }
    }
}