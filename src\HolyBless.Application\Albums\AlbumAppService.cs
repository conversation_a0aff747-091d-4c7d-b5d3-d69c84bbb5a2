using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Permissions;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Local;

namespace HolyBless.Albums
{
    [Authorize(HolyBlessPermissions.Albums.Default)]
    public class AlbumAppService : ReadOnlyAlbumAppService, IAlbumAppService
    {
        public AlbumAppService(
            IRepository<Album, int> albumRepository,
            IRepository<AlbumToFile> albumToFileRepository,
            IRepository<BucketFile, int> bucketFileRepository,
            IRepository<Channel, int> channelRepository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService,
            ILocalEventBus localEventBus
            )
            : base(albumRepository, albumToFileRepository, bucketFileRepository, channelRepository, requestContextService, cachedFileUrlAppService, localEventBus)
        {
        }

        /// <summary>
        /// [Admin] Create a new album
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Create)]
        public async Task<AlbumDto> CreateAsync(CreateUpdateAlbumDto input)
        {
            var album = ObjectMapper.Map<CreateUpdateAlbumDto, Album>(input);
            album = await _albumRepository.InsertAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        /// <summary>
        /// [Admin] Update an existing album
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumDto> UpdateAsync(int id, CreateUpdateAlbumDto input)
        {
            var album = await _albumRepository.GetAsync(id);
            ObjectMapper.Map(input, album);
            album = await _albumRepository.UpdateAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        /// <summary>
        /// [Admin] Delete Album By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Delete)]
        public async Task DeleteAsync(int id)
        {
            var albumFiles = await _albumToFileRepository.GetListAsync(af => af.AlbumId == id);
            await _albumToFileRepository.DeleteManyAsync(albumFiles, autoSave: true);
            await _albumRepository.DeleteAsync(id);
        }

        /// <summary>
        /// [Admin] Add files to an album
        /// </summary>
        /// <param name="albumId"></param>
        /// <param name="files"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<List<AlbumFileDto>> AddFilesToAlbumAsync(int albumId, List<CreateUpdateAlbumToFileDto> files)
        {
            if (files == null || files.Count == 0)
            {
                return [];
            }

            // Verify album exists
            _ = await _albumRepository.GetAsync(albumId);

            var fileIds = files.Select(f => f.FileId).Distinct().ToList();

            // Batch check: Get existing album files and bucket files in single queries
            var existingAlbumFilesTask = _albumToFileRepository.GetListAsync(
                af => af.AlbumId == albumId && fileIds.Contains(af.FileId));

            var bucketFilesTask = _bucketFileRepository.GetListAsync(
                bf => fileIds.Contains(bf.Id));

            // Get current max weight for auto-assignment
            var maxWeightTask = _albumToFileRepository.GetQueryableAsync()
                .ContinueWith(async queryableTask =>
                {
                    var queryable = await queryableTask;
                    return await AsyncExecuter.MaxAsync(
                        queryable.Where(af => af.AlbumId == albumId && !af.IsDeleted),
                        af => (int?)af.Weight) ?? 0;
                }).Unwrap();

            await Task.WhenAll(existingAlbumFilesTask, bucketFilesTask, maxWeightTask);

            var existingAlbumFiles = await existingAlbumFilesTask;
            var bucketFiles = await bucketFilesTask;
            var currentMaxWeight = await maxWeightTask;

            // Create lookup sets for O(1) existence checks
            var existingFileIds = existingAlbumFiles.Select(af => af.FileId).ToHashSet();
            var validBucketFileIds = bucketFiles.Select(bf => bf.Id).ToHashSet();

            var albumToFiles = new List<AlbumToFile>();
            var nextWeight = currentMaxWeight + 1;

            foreach (var fileDto in files)
            {
                // Skip if file already exists in album
                if (existingFileIds.Contains(fileDto.FileId))
                {
                    continue;
                }

                // Verify bucket file exists
                if (!validBucketFileIds.Contains(fileDto.FileId))
                {
                    throw new EntityNotFoundException(typeof(BucketFile), fileDto.FileId);
                }

                // Use provided weight or auto-assign next available weight
                var weight = fileDto.Weight > 0 ? fileDto.Weight : nextWeight++;

                var albumToFile = new AlbumToFile
                {
                    AlbumId = albumId,
                    FileId = fileDto.FileId,
                    Title = fileDto.Title,
                    Weight = weight
                };

                albumToFiles.Add(albumToFile);
            }

            // Batch insert all new album files
            if (albumToFiles.Count > 0)
            {
                await _albumToFileRepository.InsertManyAsync(albumToFiles, autoSave: true);
            }

            return ObjectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumToFiles);
        }

        /// <summary>
        /// [Admin] Remove a file from an Album
        /// </summary>
        /// <param name="albumId"></param>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task RemoveFileFromAlbumAsync(int albumId, int fileId)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == albumId && af.FileId == fileId);

            if (albumToFile != null)
            {
                await _albumToFileRepository.DeleteAsync(albumToFile);
            }
        }

        /// <summary>
        /// [Admin]: Update AlbumToFile like Title and Weight
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="EntityNotFoundException"></exception>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumFileDto> UpdateAlbumFileAsync(CreateUpdateAlbumToFileDto input)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == input.AlbumId && af.FileId == input.FileId);

            if (albumToFile == null)
            {
                throw new EntityNotFoundException(typeof(AlbumToFile), $"AlbumId: {input.AlbumId}, FileId: {input.FileId}");
            }

            ObjectMapper.Map(input, albumToFile);
            albumToFile = await _albumToFileRepository.UpdateAsync(albumToFile, autoSave: true);

            return ObjectMapper.Map<AlbumToFile, AlbumFileDto>(albumToFile);
        }
        /// <summary>
        /// [Admin] Re-Order Album's Files per paramter fileIds's order
        /// </summary>
        /// <param name="albumId"></param>
        /// <param name="fileIds"></param>
        /// <returns></returns>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task ReorderAlbumFilesAsync(int albumId, List<int> fileIds)
        {
            if (fileIds == null || fileIds.Count == 0)
            {
                return;
            }

            // Get all album files that need reordering in a single query
            var albumFiles = await _albumToFileRepository.GetListAsync(
                af => af.AlbumId == albumId && fileIds.Contains(af.FileId));

            if (albumFiles.Count == 0)
            {
                return;
            }

            // Create a dictionary for O(1) lookup by FileId
            var albumFileDict = albumFiles.ToDictionary(af => af.FileId, af => af);
            var filesToUpdate = new List<AlbumToFile>();

            // Update weights based on the order in fileIds list
            for (int i = 0; i < fileIds.Count; i++)
            {
                if (albumFileDict.TryGetValue(fileIds[i], out var albumFile) && albumFile.Weight != i)
                {
                    albumFile.Weight = i;
                    filesToUpdate.Add(albumFile);
                }
            }

            // Batch update all changed files in a single transaction
            if (filesToUpdate.Count > 0)
            {
                await _albumToFileRepository.UpdateManyAsync(filesToUpdate, autoSave: true);
            }
        }

        /// <summary>
        /// [Admin]: Link an album to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="albumId"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Albums.Default)]
        public async Task LinkToChannel(int channelId, int albumId)
        {
            var album = await _albumRepository.GetAsync(albumId);
            album.ChannelId = channelId;
            await _albumRepository.UpdateAsync(album, true);
        }

        /// <summary>
        /// [Admin]: Link multiple albums to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="albumIds"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Albums.Default)]
        public async Task LinkToChannel(int channelId, List<int> albumIds)
        {
            var albums = await _albumRepository.GetListAsync(x => albumIds.Contains(x.Id));
            foreach (var album in albums)
            {
                album.ChannelId = channelId;
            }
            await _albumRepository.UpdateManyAsync(albums, autoSave: true);
        }

        /// <summary>
        ///  [Admin]: Unlink an album from a channel (Set ChannelId to null)
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(int albumId)
        {
            var album = await _albumRepository.GetAsync(albumId);
            album.ChannelId = null;
            await _albumRepository.UpdateAsync(album, true);
        }

        /// <summary>
        ///  [Admin]: Unlink multiple albums from their channels (Set ChannelId to null)
        /// </summary>
        /// <param name="albumIds"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(List<int> albumIds)
        {
            var albums = await _albumRepository.GetListAsync(x => albumIds.Contains(x.Id));
            foreach (var album in albums)
            {
                album.ChannelId = null;
            }
            await _albumRepository.UpdateManyAsync(albums, autoSave: true);
        }

        /// <summary>
        /// [Admin] Get Album by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="EntityNotFoundException"></exception>
        public async Task<AlbumDto> GetAsync(int id)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var album = await queryable
                .Include(x => x.Channel)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (album == null)
            {
                throw new EntityNotFoundException(typeof(Album), id);
            }

            var rt = ObjectMapper.Map<Album, AlbumDto>(album);
            await FillThumbnailUrl(rt);
            return rt;
        }

        /// <summary>
        /// [Admin] Get all albums, optionally filtered by language code.
        /// </summary>
        /// <param name="languageCode">Optional language code to filter albums. If null, returns albums from all languages.</param>
        /// <returns>List of all albums matching the language criteria</returns>
        public async Task<List<AlbumDto>> GetAllAlbumsAsync(string? languageCode = null)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.Channel)
                .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
                .OrderBy(x => x.Weight)
                ;

            var albums = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Album>, List<AlbumDto>>(albums);
        }

        /// <summary>
        /// [Admin] Get all albums for a specific channel.
        /// </summary>
        /// <param name="channelId">The channel ID to filter albums.</param>
        /// <returns>List of albums belonging to the channel.</returns>
        public async Task<List<AlbumDto>> GetAlbumsByChannelAsync(int channelId)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.Channel)
                .Where(x => x.ChannelId == channelId)
                .OrderBy(x => x.Weight);

            var albums = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Album>, List<AlbumDto>>(albums);
        }

        /// <summary>
        /// [Admin] Move an album and reorder by weight relative to a beforeId within the same channel.
        /// </summary>
        /// <param name="albumId">The album being moved</param>
        /// <param name="beforeId">The album that the moved album should be placed before. If null, places at the end.</param>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task MoveAlbumAsync(int albumId, int? beforeId)
        {
            var albumEntity = await _albumRepository.GetAsync(albumId);
            var queryable = await _albumRepository.GetQueryableAsync();

            if (beforeId.HasValue)
            {
                // Insert before a specific album
                var beforeEntity = await _albumRepository.GetAsync(beforeId.Value);
                var insertWeight = beforeEntity.Weight;

                // Shift albums with weight >= insertWeight
                var albumsToShift = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ChannelId == albumEntity.ChannelId
                        && x.Id != albumId
                        && x.Weight >= insertWeight)
                );

                foreach (var album in albumsToShift)
                {
                    album.Weight += 1;
                }

                if (albumsToShift.Count > 0)
                {
                    await _albumRepository.UpdateManyAsync(albumsToShift, autoSave: false);
                }

                albumEntity.Weight = insertWeight;
            }
            else
            {
                // Move to the last position
                var siblings = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ChannelId == albumEntity.ChannelId
                        && x.Id != albumId)
                );

                var maxWeight = siblings.Count > 0 ? siblings.Max(x => x.Weight) : 0;
                albumEntity.Weight = maxWeight + 1;
            }

            await _albumRepository.UpdateAsync(albumEntity, autoSave: true);
        }

        /// <summary>
        /// [Admin] Move a file within an album and reorder by weight relative to a beforeFileId.
        /// </summary>
        /// <param name="fileId">The file being moved</param>
        /// <param name="beforeFileId">The file that the moved file should be placed before. If null, places at the end.</param>
        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task MoveAlbumFileAsync(int fileId, int? beforeFileId)
        {
            // Get the file being moved
            var fileEntity = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.FileId == fileId);

            if (fileEntity == null)
            {
                throw new EntityNotFoundException(typeof(AlbumToFile), fileId);
            }

            var queryable = await _albumToFileRepository.GetQueryableAsync();

            if (beforeFileId.HasValue)
            {
                // Insert before a specific file
                var beforeEntity = await _albumToFileRepository.FirstOrDefaultAsync(
                    af => af.FileId == beforeFileId.Value && af.AlbumId == fileEntity.AlbumId);

                if (beforeEntity == null)
                {
                    throw new EntityNotFoundException(typeof(AlbumToFile), beforeFileId.Value);
                }

                var insertWeight = beforeEntity.Weight;

                // Shift files with weight >= insertWeight within the same album
                var filesToShift = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.AlbumId == fileEntity.AlbumId
                        && x.FileId != fileId
                        && x.Weight >= insertWeight
                        && !x.IsDeleted)
                );

                foreach (var file in filesToShift)
                {
                    file.Weight += 1;
                }

                if (filesToShift.Count > 0)
                {
                    await _albumToFileRepository.UpdateManyAsync(filesToShift, autoSave: false);
                }

                fileEntity.Weight = insertWeight;
            }
            else
            {
                // Move to the last position
                var siblings = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.AlbumId == fileEntity.AlbumId
                        && x.FileId != fileId
                        && !x.IsDeleted)
                );

                var maxWeight = siblings.Count > 0 ? siblings.Max(x => x.Weight) : 0;
                fileEntity.Weight = maxWeight + 1;
            }

            await _albumToFileRepository.UpdateAsync(fileEntity, autoSave: true);
        }
    }
}