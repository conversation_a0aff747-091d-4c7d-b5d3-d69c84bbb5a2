using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.EventHandlerBase;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Albums.Events
{
    public class AlbumFilesRequestedEventHandler : BaseContextEventHandler, ILocalEventHandler<AlbumFilesRequestedEvent>
    {
        private readonly IRepository<AlbumToFile> _albumToFileRepository;

        public AlbumFilesRequestedEventHandler(
            IRepository<AlbumToFile> albumToFileRepository,
            IObjectMapper objectMapper,
            ICachedFileUrlAppService cachedFileUrlAppService)
            : base(objectMapper, cachedFileUrlAppService)
        {
            _albumToFileRepository = albumToFileRepository;
        }

        public async Task HandleEventAsync(AlbumFilesRequestedEvent eventData)
        {
            var albumId = eventData.AlbumId;
            var queryable = await _albumToFileRepository.GetQueryableAsync();

            var albumFiles = await queryable
                .Include(af => af.Album)
                .Include(af => af.BucketFile)
                .Where(af => af.AlbumId == albumId && !af.IsDeleted)
                .OrderBy(af => af.Weight)
                .ThenByDescending(af => af.BucketFile.DeliveryDate)
                .ToListAsync();

            var rt = new AlbumAggregateDto();
            if (albumFiles.Count > 0)
            {
                // Get the first album file to fill in the album information
                var album = albumFiles[0].Album;
                rt = _objectMapper.Map<Album, AlbumAggregateDto>(album);

                var rtList = _objectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumFiles);
                await FillThumbnailUrl(rt);
                await FillFileUrls(rtList);
                rt.AlbumFiles = rtList;
            }

            eventData.Result = rt;
        }
    }
}
