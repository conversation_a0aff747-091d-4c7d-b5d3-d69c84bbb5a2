using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Interfaces;
using HolyBless.Enums;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Albums.Events
{
    public class AlbumFilesRequestedEventHandler : ILocalEventHandler<AlbumFilesRequestedEvent>
    {
        private readonly IRepository<AlbumToFile> _albumToFileRepository;
        private readonly IObjectMapper _objectMapper;
        private readonly ICachedFileUrlAppService _cachedFileUrlAppService;

        public AlbumFilesRequestedEventHandler(
            IRepository<AlbumToFile> albumToFileRepository,
            IObjectMapper objectMapper,
            ICachedFileUrlAppService cachedFileUrlAppService)
        {
            _albumToFileRepository = albumToFileRepository;
            _objectMapper = objectMapper;
            _cachedFileUrlAppService = cachedFileUrlAppService;
        }

        public async Task HandleEventAsync(AlbumFilesRequestedEvent eventData)
        {
            var albumId = eventData.AlbumId;
            var queryable = await _albumToFileRepository.GetQueryableAsync();

            var albumFiles = await queryable
                .Include(af => af.Album)
                .Include(af => af.BucketFile)
                .Where(af => af.AlbumId == albumId && !af.IsDeleted)
                .OrderBy(af => af.Weight)
                .ThenByDescending(af => af.BucketFile.DeliveryDate)
                .ToListAsync();

            var rt = new AlbumAggregateDto();
            if (albumFiles.Count > 0)
            {
                // Get the first album file to fill in the album information
                var album = albumFiles[0].Album;
                rt = _objectMapper.Map<Album, AlbumAggregateDto>(album);

                var rtList = _objectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumFiles);
                await FillThumbnailUrl(rt);
                await FillFileUrls(rtList);
                rt.AlbumFiles = rtList;
            }

            eventData.Result = rt;
        }

        private async Task FillThumbnailUrl<T>(T albumDto) where T : IHaveThumbnail
        {
            if (_cachedFileUrlAppService == null || albumDto.ThumbnailFileId == null)
            {
                return;
            }
            //Thumbnail always uses CloudFlare
            var preferProvider = ProviderCodeConstants.CloudFlare;
            albumDto.ThumbnailUrl = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
                albumDto.ThumbnailFileId.Value, preferProvider);
        }

        private async Task FillFileUrls<T>(List<T> albumDtos) where T : IHaveFileUrl
        {
            var preferProvider = ProviderCodeConstants.CloudFlare;
            if (_cachedFileUrlAppService == null || albumDtos.Count == 0)
            {
                return;
            }
            
            var ids = albumDtos.Select(x => x.FileId).ToList();
            var urls = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
                ids,
                preferProvider
            );
            foreach (var al in albumDtos)
            {
                al.FileUrl = urls.TryGetValue(al.FileId, out var url) ? url : null;
            }
        }
    }
}
